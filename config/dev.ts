module.exports = {
  env: {
    NODE_ENV: '"development"',
  },
  defineConstants: {
    SERVER_URL: '"http://43.136.100.101:20000/dkx-app"', //本地
    // ASSETS_URL: '"http://43.136.100.101:20000/dkx-app/base/v1.0/files/common/app-base"', //本地
    // SERVER_URL: '"https://ga.swcares.com.cn/trade/openapi/routing/dkx-app"',
    ASSETS_URL:
      '"https://ga.swcares.com.cn/trade/openapi/routing/dkx-app/base/v1.0/files/common/app-base"',
  },
  mini: {},
  h5: {
    esnextModules: ['taro-ui'],
  },
};
