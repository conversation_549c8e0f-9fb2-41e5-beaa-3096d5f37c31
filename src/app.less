@import './styles/variables.less';

page {
  font-size: 14px;
  color: @color-text-primary;
  background: #fafafa;
}

view {
  box-sizing: border-box;
}

image {
  width: 100%;
  display: block;
}

Button {
  font-size: 16px;
  &::before,
  &::after {
    border: none;
  }
}
//.at-icon {
//  transform: scale(0.5);
//}
wx-button[type='primary'] {
  background-color: @color-primary;
  color: #fff;
  &:active {
    background-color: @color-primary-disable;
  }
}
wx-button[disabled][type='primary'] {
  background-color: @color-primary-disable;
}
