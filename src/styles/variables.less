// 变量参考： node_modules/@nutui/nutui-react-taro/dist/styles/theme-default.scss
@color-primary: var(--color-primary); /*主色*/
@color-primary-disable: rgba(20, 102, 225, 0.8); /*主色*/

@color-text-primary: #2b2b32;

@color-blue-f8: #1663f8;

@color-white: #fff;

@color-black: #000;

@color-gray-99: #999999;
@color-gray-66: #666666;
@color-gray-33: #333333;
@color-gray-cc: #cccccc;
@color-gray-98: #828998;

@color-error: #f3564b;

page {
  --color-primary: #1466e1;

  --nutui-color-primary: var(--color-primary);
  --nutui-color-primary-stop-1: var(--color-primary);
  --nutui-color-primary-stop-2: var(--color-primary);
  //tab
  --nutui-tabs-titles-item-active-color: @color-text-primary;
  --nutui-tabs-line-border-radius: 12px;
  --nutui-tabs-titles-background-color: #e8f0ff;
  --nutui-tabs-titles-item-color: #525b67;
  --nutui-tabs-titles-height: 36px;
  //cell
  --nutui-cell-title-color: @color-text-primary;
  --nutui-cell-group-title-color: @color-text-primary;
  --nutui-cell-group-wrap-margin: 0;
  //form
  --nutui-form-item-error-line-color: @color-error;
  --nutui-form-item-required-color: @color-error;
  --nutui-form-item-error-message-color: @color-error;
  --nutui-form-item-label-font-size: 14px;
  //button
  --nutui-button-background-color: @color-primary;
  --nutui-button-primary-disabled: @color-primary-disable;
  --nutui-button-primary-disabled-color: rgba(255, 255, 255, 0.5);
  --nut-button-primary-children: transparent;
}
.nut-button-primary-children {
  background: transparent;
}
