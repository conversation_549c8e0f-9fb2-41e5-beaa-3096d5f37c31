.safe {
  &-mb {
    margin-bottom: @safe-bottom-constant;
    margin-bottom: @safe-bottom-env;
  }

  &-pb {
    padding-bottom: @safe-bottom-constant;
    padding-bottom: @safe-bottom-env;
  }
}

.bg {
  &-primary {
    background: @color-primary;
  }
  &-white {
    background: white;
  }
  &-transparent {
    background: transparent;
  }
  &-F2F6FC {
    background: #f2f6fc;
  }
  &-FFD46536 {
    background: #ffd46536;
  }
  &-FF7346 {
    background: #ff7346;
  }
  &-F3564B {
    background: #f3564b;
  }
  &-F84F2A1A {
    background: #f84f2a1a;
  }
  &-FFF3EB {
    background: #fff3eb;
  }
  &-737578 {
    background: #737578;
  }
  &-F4F8FD {
    background: #f4f8fd;
  }
  &-FAF2F0 {
    background: #faf2f0;
  }
  &-00000014 {
    background: #00000014;
  }
  &-F7F8FA {
    background: #f7f8fa;
  }
  &-EBF1FF {
    background: #ebf1ff;
  }
  &-F84F2A1A {
    background: #f84f2a1a;
  }
  &-124072 {
    background: #124072;
  }
  &-red-linear {
    background: linear-gradient(90deg, #f3564b 0%, #ff8025 100%);
  }
}

.min {
  &-w {
    &-90 {
      min-width: 90px;
    }
  }

  &-h {
    &-screen {
      min-height: 100vh;
    }
  }
}

.max {
  &-w {
    &-90vw {
      max-width: 90vw;
    }
  }
  &-h {
    &-360 {
      max-height: 360px;
    }
  }
}

.w {
  &-full {
    width: 100%;
  }
  &-screen {
    width: 100vw;
  }
  &-auto {
    width: auto;
  }
  &-4 {
    width: 4px;
  }
  &-12 {
    width: 12px;
  }
  &-14 {
    width: 14px;
  }
  &-18 {
    width: 18px;
  }
  &-20 {
    width: 20px;
  }
  &-24 {
    width: 24px;
  }
  &-40 {
    width: 40px;
  }
  &-54 {
    width: 54px;
  }
  &-80 {
    width: 80px;
  }
  &-92 {
    width: 92px;
  }
  &-100 {
    width: 100px;
  }
  &-264 {
    width: 264px;
  }
  &-reason-item {
    width: calc((100% - 24px) / 3); // 考虑到间距是12px，三列布局
  }
}

.h {
  &-full {
    height: 100%;
  }
  &-screen {
    height: 100vh;
  }
  &-auto {
    height: auto;
  }
  &-4 {
    height: 4px;
  }
  &-12 {
    height: 12px;
  }
  &-14 {
    height: 14px;
  }
  &-18 {
    height: 18px;
  }
  &-20 {
    height: 20px;
  }
  &-24 {
    height: 24px;
  }
  &-28 {
    height: 28px;
  }
  &-32 {
    height: 32px;
  }
  &-34 {
    height: 34px;
  }
  &-36 {
    height: 36px;
  }
  &-40 {
    height: 40px;
  }
  &-44 {
    height: 44px;
  }
  &-48 {
    height: 48px;
  }
  &-50 {
    height: 50px;
  }
  &-54 {
    height: 54px;
  }
  &-84 {
    height: 84px;
  }
  &-100 {
    height: 100px;
  }
  &-180 {
    height: 180px;
  }
  &-184 {
    height: 184px;
  }
  &-200 {
    height: 200px;
  }
  &-360 {
    height: 360px;
  }
}

.leading {
  &-normal {
    line-height: 1.5;
  }

  &-28 {
    line-height: 28px;
  }
  &-48 {
    line-height: 48px;
  }
}

.m {
  &-0 {
    margin: 0;
  }
  &-auto {
    margin: auto;
  }
  &-8 {
    margin: 8px;
  }
}
// ---------------------
.mt {
  &-8 {
    margin-top: 8px;
  }
  &-12 {
    margin-top: 12px;
  }
  &-16 {
    margin-top: 16px;
  }
  &-24 {
    margin-top: 24px;
  }
  &-180 {
    margin-top: 180px;
  }
}
// ---------------------
.mr {
  &-2 {
    margin-right: 2px;
  }
  &-4 {
    margin-right: 4px;
  }
  &-6 {
    margin-right: 4px;
  }
  &-8 {
    margin-right: 8px;
  }
  &-12 {
    margin-right: 12px;
  }
  &-10 {
    margin-right: 10px;
  }
}
.ml {
  &-2 {
    margin-left: 2px;
  }
  &-4 {
    margin-left: 4px;
  }
  &-8 {
    margin-left: 8px;
  }
  &-16 {
    margin-left: 16px;
  }
}
// ---------------------
.mb {
  &-2 {
    margin-bottom: 2px;
  }
  &-4 {
    margin-bottom: 4px;
  }
  &-8 {
    margin-bottom: 8px;
  }
  &-12 {
    margin-bottom: 12px;
  }
  &-16 {
    margin-bottom: 16px;
  }
  &-20 {
    margin-bottom: 20px;
  }
}
// ---------------------
.mx {
  &-auto {
    margin-left: auto;
    margin-right: auto;
  }
  &-0 {
    margin-left: 0;
    margin-right: 0;
  }
  &-2 {
    margin-left: 2px;
    margin-right: 2px;
  }
  &-4 {
    margin-left: 4px;
    margin-right: 4px;
  }
  &-6 {
    margin-left: 6px;
    margin-right: 6px;
  }
}

.my {
  &-auto {
    margin-top: auto;
    margin-bottom: auto;
  }
  &-0 {
    margin-top: 0;
    margin-bottom: 0;
  }
}

.p {
  &-0 {
    padding: 0;
  }
  &-4 {
    padding: 4px;
  }
  &-8 {
    padding: 8px;
  }
  &-10 {
    padding: 10px;
  }
  &-12 {
    padding: 12px;
  }
  &-16 {
    padding: 16px;
  }
}

.pt {
  &-6 {
    padding-top: 6px;
  }
  &-10 {
    padding-top: 10px;
  }
  &-12 {
    padding-top: 12px;
  }
  &-16 {
    padding-top: 16px;
  }
}
.pl {
  &-6 {
    padding-left: 6px;
  }
  &-10 {
    padding-left: 10px;
  }
  &-12 {
    padding-left: 12px;
  }
  &-16 {
    padding-left: 16px;
  }
}
.pb {
  &-8 {
    padding-bottom: 8px;
  }
  &-12 {
    padding-bottom: 12px;
  }
  &-16 {
    padding-bottom: 16px;
  }
  &-24 {
    padding-bottom: 24px;
  }
  &-32 {
    padding-bottom: 32px;
  }
}

.px {
  &-0 {
    padding-left: 0;
    padding-right: 0;
  }
  &-2 {
    padding-left: 2px;
    padding-right: 2px;
  }
  &-4 {
    padding-left: 4px;
    padding-right: 4px;
  }
  &-6 {
    padding-left: 6px;
    padding-right: 6px;
  }
  &-8 {
    padding-left: 8px;
    padding-right: 8px;
  }
  &-10 {
    padding-left: 10px;
    padding-right: 10px;
  }
  &-12 {
    padding-left: 12px;
    padding-right: 12px;
  }
  &-16 {
    padding-left: 16px;
    padding-right: 16px;
  }
  &-20 {
    padding-left: 20px;
    padding-right: 20px;
  }
  &-32 {
    padding-left: 32px;
    padding-right: 32px;
  }
}

.py {
  &-0 {
    padding-top: 0;
    padding-bottom: 0;
  }
  &-2 {
    padding-top: 2px;
    padding-bottom: 2px;
  }
  &-4 {
    padding-top: 4px;
    padding-bottom: 4px;
  }
  &-6 {
    padding-top: 6px;
    padding-bottom: 6px;
  }
  &-8 {
    padding-top: 8px;
    padding-bottom: 8px;
  }
  &-12 {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

.flex {
  display: flex;

  &-1 {
    flex: 1;
  }

  &-row {
    flex-direction: row;
  }
  &-col {
    flex-direction: column;
  }
  &-wrap {
    flex-wrap: wrap;
  }
  &-nowrap {
    flex-wrap: nowrap;
  }

  &-shrink {
    &-0 {
      flex-shrink: 0;
    }
  }
}

.items {
  &-center {
    align-items: center;
  }
  &-start {
    align-items: flex-start;
  }
  &-end {
    align-items: flex-end;
  }
  &-stretch {
    align-items: stretch;
  }
  &-baseline {
    align-items: baseline;
  }
}

.justify {
  &-center {
    justify-content: center;
  }
  &-start {
    justify-content: flex-start;
  }
  &-end {
    justify-content: flex-end;
  }
  &-between {
    justify-content: space-between;
  }
  &-around {
    justify-content: space-around;
  }
  &-evenly {
    justify-content: space-evenly;
  }
}

.rounded {
  &-none {
    border-radius: 0;
  }
  &-full {
    border-radius: 9999px;
  }
  &-4 {
    border-radius: 4px;
  }
  &-6 {
    border-radius: 6px;
  }
  &-8 {
    border-radius: 8px;
  }
  &-12 {
    border-radius: 12px;
  }
}

// 圆角相关
.rounded-t {
  &-12 {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }
}

.border {
  &-0 {
    border-width: 0;
    border-style: solid;
  }
  &-1 {
    border-width: 1px;
    border-style: solid;
  }
  &-2 {
    border-width: 2px;
    border-style: solid;
  }
  &-4 {
    border-width: 4px;
    border-style: solid;
  }

  // ---------------------
  &-t {
    &-1 {
      border-width: 0;
      border-top-width: 1px;
      border-style: solid;
    }
    &-2 {
      border-width: 0;
      border-top-width: 2px;
      border-style: solid;
    }
  }
  // ---------------------
  &-r {
    &-1 {
      border-width: 0;
      border-right-width: 1px;
      border-style: solid;
    }
  }
  // ---------------------
  &-b {
    &-1 {
      border-width: 0;
      border-bottom-width: 1px;
      border-style: solid;
    }
  }
  // ---------------------
  &-l {
    &-1 {
      border-width: 0;
      border-left-width: 1px;
      border-style: solid;
    }
  }

  // 边框颜色
  &-E6EAF0 {
    border-color: #e6eaf0;
  }
  &-FF7346 {
    border-color: #ff7346;
  }
  &-white {
    border-color: white;
  }
  &-primary {
    border-color: @color-primary;
  }
  &-ACADFF {
    border-color: #acadff;
  }
  &-9AA2CA {
    border-color: #9aa2ca;
  }
  &-00000014 {
    border-color: #00000014;
  }
  &-124072 {
    border-color: #124072;
  }
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.border-dotted {
  border-style: dotted;
}

.border-double {
  border-style: double;
}

.border-none {
  border-style: none;
}

.cursor {
  &-pointer {
    cursor: pointer;
  }
  &-default {
    cursor: default;
  }
  &-NotAllowed {
    cursor: not-allowed;
  }
  &-wait {
    cursor: wait;
  }
}

.opacity {
  &-0 {
    opacity: 0;
  }
  &-25 {
    opacity: 0.25;
  }
  &-50 {
    opacity: 0.5;
  }
  &-75 {
    opacity: 0.75;
  }
  &-100 {
    opacity: 1;
  }
}

.overflow {
  &-auto {
    overflow: auto;
  }
  &-hidden {
    overflow: hidden;
  }
  &-scroll {
    overflow: scroll;
  }
  &-visible {
    overflow: visible;
  }
  &-y-auto {
    overflow-y: auto;
  }
  &-y-hidden {
    overflow-y: hidden;
  }
  &-y-scroll {
    overflow-y: scroll;
  }
  &-y-visible {
    overflow-y: visible;
  }
  &-x-auto {
    overflow-x: auto;
  }
  &-x-hidden {
    overflow-x: hidden;
  }
}

.text {
  &-10 {
    font-size: 10px;
  }
  &-12 {
    font-size: 12px;
  }
  &-14 {
    font-size: 14px;
  }
  &-16 {
    font-size: 16px;
  }
  &-20 {
    font-size: 20px;
  }

  // ---------------------
  &-center {
    text-align: center;
  }
  &-left {
    text-align: left;
  }
  &-right {
    text-align: right;
  }
  // ---------------------
  &-white {
    color: #fff;
  }
  &-primary {
    color: @color-primary;
  }
  &-737578 {
    color: #737578;
  }
  &-C4C7CA {
    color: #c4c7ca;
  }
  &-FF7346 {
    color: #ff7346;
  }
  &-FFF3EB {
    color: #fff3eb;
  }
  &-191919 {
    color: #191919;
  }
  &-F84F2A {
    color: #f84f2a;
  }
  &-1D1F20 {
    color: #1d1f20;
  }
  &-4F5170 {
    color: #4f5170;
  }
  &-9AA2CA {
    color: #9aa2ca;
  }
  &-666666 {
    color: #666666;
  }
  &-23242D {
    color: #23242d;
  }
  &-6F7C8F {
    color: #6f7c8f;
  }
  &-717C8C {
    color: #717c8c;
  }
  &-124072 {
    color: #124072;
  }
  &-1663f8 {
    color: #1663f8;
  }
}

.font {
  &-thin {
    font-weight: 100;
  }
  &-light {
    //font-weight: 300;
    font-weight: lighter;
  }
  &-normal {
    //font-weight: 400 ;
    font-weight: normal;
  }
  &-medium {
    font-weight: 500;
  }
  &-bold {
    //font-weight: 600;
    font-weight: bold;
  }
  &-bolder {
    //font-weight: 700;
    font-weight: bolder;
  }
  &-extrabold {
    font-weight: bolder;
  }
}

.line-through {
  text-decoration: line-through;
}

// 定位相关类
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.static {
  position: static;
}

// z-index相关类
.z {
  &-0 {
    z-index: 0;
  }
  &-10 {
    z-index: 10;
  }
  &-20 {
    z-index: 20;
  }
  &-30 {
    z-index: 30;
  }
  &-40 {
    z-index: 40;
  }
  &-50 {
    z-index: 50;
  }
  &-max {
    z-index: 9999;
  }
  &-bottom {
    z-index: -1;
  }
}

// 位置相关类
.top {
  &-0 {
    top: 0;
  }
  &-7 {
    top: 7px;
  }
  &-10 {
    top: 10px;
  }
  &-180 {
    top: 180px;
  }
  &-full {
    top: 100%;
  }
}

.right {
  &-0 {
    right: 0;
  }
  &-full {
    right: 100%;
  }
}

.bottom {
  &-0 {
    bottom: 0;
  }
  &-full {
    bottom: 100%;
  }
}

.left {
  &-0 {
    left: 0;
  }
  &-full {
    left: 100%;
  }
}

// 定位辅助类
.inset {
}

// 变换相关类
.translate {
}

.box-border {
  box-sizing: border-box;
}
.box-content {
  box-sizing: content-box;
}

.overflow-hidden {
  overflow: hidden;
}

.shadow {
  &-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  &-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  &-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  &-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  &-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
  &-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  }
  &-none {
    box-shadow: none;
  }
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp {
  &-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  &-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  &-3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

// Gap 间距
.gap {
  &-6 {
    gap: 6px;
  }
  &-12 {
    gap: 12px;
  }
}

.underline {
  text-decoration: underline;
}
.white-space {
  &-nowrap {
    white-space: nowrap;
  }
}
