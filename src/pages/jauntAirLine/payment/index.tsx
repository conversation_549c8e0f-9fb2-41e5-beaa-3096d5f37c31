import { memo } from 'react';
import { View, Text, ScrollView, Image } from '@tarojs/components';
import { Button, Dialog } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { wx } from '@/utils/img';
import { getUrlParam, goBack, maskCertNo } from '@/utils';
import './index.less';
import '../selectCabin/index.less';
import { ArrowRight } from '@nutui/icons-react-taro';
import FlightItem from '../components/FlightItem';
import BottomCtr from '@/components/BottomCtr';
import { handleCancel, handlePay } from '@/pages/orders/jauntOrder';
import { getCertTypeName } from '@/pages/userCenter/addPassenger/contant';
import PriceDetail from '@/components/PriceDetail';
import dayjs from 'dayjs';

const Payment = () => {
  const pageDate = getUrlParam('data') ? JSON.parse(getUrlParam('data') || '') : null;
  const pageType = getUrlParam('pageType');

  // console.log(pageDate, pageType);
  const onPay = () => {
    if (pageType === 'jaunt') {
      handlePay(pageDate?.orderNo, pageDate?.orderPrice);
    }
    if (pageType === 'travel') {
      handlePay(pageDate?.orderNo, pageDate?.price);
    }
  };

  return (
    <View className='select-cabin-page payment-page'>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'订单支付'} />

      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        {/* 支付倒计时提示 */}
        <View className='card-box payment-timer'>
          <Text className='timer-text'>请在15分钟内尽快完成支付</Text>
          <View className='order-info'>
            <Text className='order-label'>订单号：</Text>
            <Text className='order-value'>{pageDate?.orderNo}</Text>
          </View>
          <View className='order-info'>
            <Text className='order-label'>截止时间：</Text>
            <Text className='order-value'>
              {dayjs(pageDate?.createTime).add(15, 'minute').format('YYYY-MM-DD HH:mm:ss')}
            </Text>
            {/*<Text className='order-value'>{pageDate?.createTime}</Text>*/}
          </View>
        </View>

        {/* 支付方式 */}
        <View className='card-box payment-method'>
          <View className='section-title'>支付方式</View>
          <View className='method-item selected'>
            <View className='method-left'>
              <Image src={wx} className='method-icon' />
              <Text className='method-name'>微信支付</Text>
            </View>
            <View className='method-right'>
              <View className='check-icon'></View>
            </View>
          </View>
        </View>

        {/* 航班信息 */}
        {pageType === 'jaunt' && (
          <View className='card-box flight-info-section'>
            <View className='section-title'>航班信息</View>
            <View className='flight-card'>
              <View className='flight-header'>
                <Text className='flight-date'>{pageDate?.flights?.[0].flightDate}</Text>
              </View>
              <FlightItem flight={pageDate?.flights?.[0]} />
            </View>
          </View>
        )}
        {pageType === 'travel' && (
          <View className='card-box flight-info-section'>
            <View className='section-title'>产品信息</View>
            <View className='flight-card'>
              <View className='flight-header'>
                <Text className='flight-date'>出行日期:{pageDate?.product.productDate}</Text>
              </View>
              <View className={'p-12'}>
                <View className={'title-box mb-8'}>{pageDate?.product.productName}</View>
                <View className={'info-text'}>套餐类型:{pageDate?.product.packageName}</View>
                <View className={'info-text'}>套餐详情:{pageDate?.product.productDescription}</View>
                <View className={'info-text'}>套餐编号:{pageDate?.product.productNo}</View>
              </View>
            </View>
          </View>
        )}

        {/* 支付信息 */}
        <View className='card-box payment-info'>
          <View className='section-title'>支付信息</View>
          <View className='price-item' onClick={() => console.log('查看价格明细')}>
            <Text className='price-label'>实际支付</Text>
            <View className='price-value'>
              {pageType === 'jaunt' && <Text className='price'>¥{pageDate?.orderPrice}</Text>}
              {pageType === 'travel' && <Text className='price'>¥{pageDate?.price}</Text>}
              {/*<Text className='detail'>*/}
              {/*  明细 <ArrowRight size={12} color='#999' />*/}
              {/*</Text>*/}
              <PriceDetail
                priceObj={{
                  price: pageDate?.price || pageDate?.orderPrice,
                  tax: pageDate?.taxAndFees,
                }}
              />
            </View>
          </View>
        </View>

        {/* 乘机人信息 */}
        <View className='card-box passenger-info-section'>
          <View className='section-title'>乘机人信息</View>
          {pageDate?.passengers?.map(item => (
            <View key={item.idNo} className='passenger-item'>
              <View className='passenger-left'>
                <Text className='passenger-name'>
                  {item?.name || `${item?.firstName} ${item?.nextName}`}
                </Text>
                <View className='passenger-tag'>
                  {getCertTypeName(item?.idType || item?.certType)}
                </View>
              </View>
              <View className='passenger-right'>
                <Text className='id-info'>证件号：{maskCertNo(item?.idNo || item?.certNo)}</Text>
                <ArrowRight size={16} color='#999' />
              </View>
            </View>
          ))}
        </View>

        {/* 取消订单按钮 */}
        <View
          className='cancel-order'
          onClick={() =>
            handleCancel(pageDate?.orderNo, () => {
              goBack(1);
            })
          }
        >
          <Text>取消订单</Text>
        </View>
      </ScrollView>

      {/* 底部支付按钮 */}
      <BottomCtr>
        <View className='bottom-action'>
          <View className='price-info'>
            <Text className='price-symbol'>¥</Text>
            {pageType === 'jaunt' && <Text className='price-value'>{pageDate?.orderPrice}</Text>}
            {pageType === 'travel' && <Text className='price-value'>{pageDate?.price}</Text>}
          </View>
          <Button
            className='pay-btn'
            onClick={() => {
              onPay();
            }}
          >
            立即支付
          </Button>
        </View>
      </BottomCtr>

      <Dialog id='cancel' />
    </View>
  );
};

export default memo(Payment);
