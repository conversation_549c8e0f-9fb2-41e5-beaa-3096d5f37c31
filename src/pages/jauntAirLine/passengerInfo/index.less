@color-blue: #1663f8;
@color-red: #f84f2a;
@color-green: #24a743;

.passenger-info-page {
  .scrollview {
    background: transparent;
  }
  // 联系电话
  .contact-phone-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;

    .contact-phone-label {
      font-size: 14px;
      color: #1c1c1c;
    }

    .contact-phone-value {
      font-size: 14px;
      color: #737578;
    }
  }

  // 联系人信息
  .contact-info-section {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #1c1c1c;
      margin-bottom: 4px;
    }

    .section-subtitle {
      font-size: 12px;
      color: #737578;
      margin-bottom: 16px;
    }

    .contact-info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .contact-info-left {
        display: flex;
        flex-direction: column;

        .contact-label {
          font-size: 14px;
          color: #737578;
          margin-bottom: 4px;
        }
      }
      .contact-value {
        font-size: 14px;
        color: #1c1c1c;
      }
    }
  }

  // 航空保险
  .insurance-section {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #1c1c1c;
      margin-bottom: 4px;
    }

    .section-subtitle {
      font-size: 12px;
      color: #737578;
      margin-bottom: 16px;
    }

    .insurance-options {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .insurance-card {
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #f0f0f0;

      &.selected {
        border-color: @color-blue;
        background-color: rgba(22, 99, 248, 0.05);
      }

      .insurance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .insurance-left {
          display: flex;
          align-items: baseline;

          .insurance-name {
            font-size: 16px;
            font-weight: 500;
            color: #1c1c1c;
            margin-right: 8px;
          }

          .insurance-price {
            font-size: 14px;
            color: @color-red;
          }
        }
      }

      .insurance-details {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .insurance-coverage {
          font-size: 12px;
          color: #737578;
          background-color: #f5f7fa;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }
  }

  // 卡券优惠
  .discount-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    //padding: 16px;
    margin-bottom: 6px;

    .discount-label {
      font-size: 14px;
      color: #1c1c1c;
    }

    .discount-value {
      display: flex;
      align-items: center;

      .discount-amount {
        font-size: 14px;
        color: @color-red;
        margin-right: 8px;
      }
    }
  }

  // 底部操作栏
  .bottom-action {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .price-info {
      display: flex;
      align-items: baseline;

      .price-label {
        font-size: 16px;
        color: @color-red;
        font-weight: 500;
      }

      .price-value {
        font-size: 24px;
        font-weight: 500;
        color: @color-red;
      }

      .price-detail {
        font-size: 12px;
        color: #737578;
        margin-left: 8px;
      }
    }

    .submit-btn {
      width: 120px;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
    }
  }
}
.tips-img {
  width: 100%;
}
