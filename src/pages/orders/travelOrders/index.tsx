import cx from 'classnames';
import { View, Text, Image, Button } from '@tarojs/components';
import { Dialog } from '@nutui/nutui-react-taro';
import { parachute, aerialTour } from '@/utils/img';
import { judgeStatusTag } from '@/pages/orders/constant';
import { handleCancel, handlePay } from '@/pages/orders/jauntOrder';
import { toUrl } from '@/utils';

const TravelOrders = ({ orderList, getData }) => {
  return (
    <View>
      {orderList?.map((item, index) => (
        <View
          key={index}
          className={cx('bg-white shadow-sm rounded-8 p-16', {
            'mt-12': index !== 0,
          })}
        >
          <View
            onClick={() =>
              toUrl(`/pages/orders/travelOrders/orderDetails/index?orderNo=${item.orderNo}`)
            }
          >
            <View className={'flex items-center justify-between mb-12 '}>
              <View className={'info-text'}>订单号：{item?.orderNo}</View>
              <View>{judgeStatusTag(String(item?.status))}</View>
            </View>
            <View className='flex items-center justify-between mb-12'>
              <View className='flex items-center'>
                <Image
                  className='w-20 h-20 rounded-lg mr-8'
                  src={item?.travelOrderListProduct?.productType === '1' ? parachute : aerialTour}
                />
                <Text className='text-14 font-medium'>
                  {item?.travelOrderListProduct?.productName}
                </Text>
              </View>
              {/*{judgeStatusTag(String(item?.status))}*/}
            </View>

            <View className='mb-8'>
              <Text className='text-16 text-23242D font-semibold'>
                {item?.travelOrderListProduct?.packageName}
              </Text>
            </View>

            <View className='mb-8'>
              <Text className='text-14 text-4F5170 font-medium'>
                {item?.travelOrderListProduct?.packageDescription}
              </Text>
            </View>

            {/*<View className='mb-8'>*/}
            {/*  <Text className='text-12 text-9AA2CA'>*/}
            {/*    游玩时间：{item?.travelOrderListProduct?.productDate}*/}
            {/*  </Text>*/}
            {/*</View>*/}

            <View className='flex justify-end pt-4 mb-8'>
              <Text className='text-12 text-4F5170 font-medium'>订单金额：</Text>
              <Text className={'text-F84F2A'}>¥{item.price}</Text>
            </View>
          </View>
          {item?.status === 0 && (
            <View className='border-t-1 border-E6EAF0 border-dashed pt-12 mt-8 flex justify-end'>
              <View className='mr-8'>
                <Button
                  className='rounded-8 h-32 px-20 flex items-center justify-center text-14 font-medium bg-white border-1 border-9AA2CA text-4F5170'
                  onClick={() => {
                    handleCancel(item?.orderNo, getData);
                  }}
                >
                  取消
                </Button>
              </View>
              <View className=''>
                <Button
                  className='rounded-8 h-32 px-20 flex items-center justify-center text-14 font-medium bg-white border-1 border-primary text-primary'
                  onClick={() => handlePay(item?.orderNo, item?.price, getData)}
                >
                  付款
                </Button>
              </View>
            </View>
          )}
        </View>
      ))}
      <Dialog id='cancel' />
    </View>
  );
};

export default TravelOrders;
