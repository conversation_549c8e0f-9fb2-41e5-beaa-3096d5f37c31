@color-blue: #1663f8;
.pay-tips {
  height: 65vh;
  overflow-y: auto;
  .tips-row {
    overflow-x: auto;
  }
  .tips-item {
    .tips-text {
      white-space: nowrap;
    }
    &.active {
      position: relative;
      background: #fff;
      color: #2b2b32;
      font-weight: bold;
      border-radius: 12px 12px 0 0;
      &::after {
        content: '';
        position: absolute;
        bottom: 6px;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        height: 4px;
        border-radius: 12px;
        background: linear-gradient(90deg, rgba(0, 82, 217, 0) 0%, #0052d9 100%);
      }
    }
  }
  .tips-content {
    min-height: 50px;
    max-height: 100%;
    //max-height: 60vh;
    overflow-y: auto;
    text-align: left;
  }
}
// 协议勾选
.agreement-section {
  padding: 16px;
  margin-bottom: 12px;

  .agreement-text {
    font-size: 14px;
    color: #1c1c1c;
  }

  .agreement-links {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;
    padding-left: 24px;

    .agreement-link {
      font-size: 12px;
      color: @color-blue;
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
}
//.submit-btn:disabled {
//  background: rgba(22, 99, 248, 0.5);
//  color: rgba(255, 255, 255, 0.5);
//}
