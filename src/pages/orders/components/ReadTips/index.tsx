import { memo, useEffect, useRef, useState } from 'react';
import CustomActionSheet from '@/components/CustomActionSheet';
import { RichText, Text, View } from '@tarojs/components';
import { Button, Cell, Checkbox, Collapse } from '@nutui/nutui-react-taro';
import './index.less';
import { FlightPlanBO } from '@/api/servies/dkx';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';
import { ArrowDown } from '@nutui/icons-react-taro';
import BottomCtr from '@/components/BottomCtr';

interface ReadTipsProps {
  pageType: 1 | 2 | 3; //1=短途，2=低空游览，3=包机
  defaultData: any;
  show?: boolean;
  onClose?: () => void;
  onChecked?: (checked: boolean) => void;
  getData?: (isMustRead: boolean) => void;
}

const ReadTips = ({ show, onClose, pageType, getData, defaultData, onChecked }: ReadTipsProps) => {
  const [tips, setTips] = useState<any[]>([]); //须知title列表
  const [tipsTitle, setTipsTitle] = useState('');
  const [tipModalShow, setTipModalShow] = useState(false); //须知弹窗

  const [tabList, setTabList] = useState<any[]>([]); //须知tab列表
  const [tabContent, setTabContent] = useState<any>(''); //当前tab内容

  const [isDisabled, setIsDisabled] = useState(true);
  const [countNum, setCountNum] = useState(3);
  const [agreementChecked, setAgreementChecked] = useState(false); // 协议勾选
  const countdownTimer = useRef<NodeJS.Timeout | null>(null);

  //获取须知title列表
  const getTips = async (flightInfo: FlightPlanBO) => {
    if (pageType === 1) {
      if (!flightInfo) return;
      const { code, data } = await api.shortRouteV10FlightTipCreate({
        depCode: flightInfo?.departAirportCode,
        arrCode: flightInfo?.arriveAirportCode,
        flightNo: flightInfo?.flightNo,
        carrierCode: flightInfo?.carrier,
      });
      if (code === SUCCESS_CODE && data) {
        setTips(data);
      }
    } else if (pageType === 2) {
      if (!defaultData?.companyCode) return;
      const { code, data } = await api.v10TipCreate({ companyCode: defaultData?.companyCode });
      if (code === SUCCESS_CODE && data) {
        setTips(data);
      }
    }
  };
  //提交订单打开弹窗
  const getTabText = async () => {
    const { code, data } = await api.v10TipOrderBeforeDetail(
      pageType,
      defaultData?.carrier || defaultData?.companyCode
    );
    if (code === SUCCESS_CODE && data) {
      if (data.nodeType === 2) {
        setTabContent(data.text);
      } else if (data.nodeType === 1) {
        setTabList(data.tips || []);
      }
    }
  };
  //协议id打开弹窗
  const getTabContent = async (tabId: number) => {
    const { code, data } = await api.v10TipInfoDetail(pageType, tabId);
    if (code === SUCCESS_CODE && data) {
      if (data.nodeType === 2) {
        setTabContent(data.text);
      } else if (data.nodeType === 1) {
        setTabList(data.tips || []);
      }
    }
  };
  useEffect(() => {
    setTipModalShow(!!show);
    if (show) {
      getTabText();
      // 弹窗打开时启动倒计时
      setIsDisabled(true);
      setCountNum(3);
      startCountdown();
    } else {
      // 弹窗关闭时清理定时器
      if (countdownTimer.current) {
        clearInterval(countdownTimer.current);
        countdownTimer.current = null;
      }
    }
  }, [show]);
  useEffect(() => {
    if (!tipModalShow) {
      setTabList([]);
      setTabContent('');
      setTipsTitle('');
    }
  }, [tipModalShow]);
  useEffect(() => {
    if (defaultData) {
      getTips(defaultData);
    }
  }, [defaultData]);

  const startCountdown = () => {
    // 清理之前的定时器
    if (countdownTimer.current) {
      clearInterval(countdownTimer.current);
    }

    countdownTimer.current = setInterval(() => {
      setCountNum(prev => {
        if (prev <= 1) {
          if (countdownTimer.current) {
            clearInterval(countdownTimer.current);
            countdownTimer.current = null;
          }
          setIsDisabled(false); // 倒计时结束，启用按钮
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (countdownTimer.current) {
        clearInterval(countdownTimer.current);
      }
    };
  }, []);
  return (
    <>
      {/* 协议勾选 */}
      <View className='agreement-section'>
        <Checkbox
          checked={agreementChecked}
          onChange={val => {
            setAgreementChecked(val);
            onChecked?.(val);
          }}
        >
          <Text className='agreement-text'>我已确认乘客信息，且认真阅读并同意</Text>
        </Checkbox>
        <View className='agreement-links'>
          {tips?.map(item => {
            return (
              <Text
                className='agreement-link'
                key={item?.id}
                onClick={() => {
                  setTipModalShow(true);
                  getTabContent(item?.id);
                  setTipsTitle(item?.tip);
                }}
              >
                《{item?.tip}》
              </Text>
            );
          })}
        </View>
      </View>
      {/* 订单tips弹窗 */}
      <CustomActionSheet
        visible={tipModalShow}
        title={tipsTitle || '温馨提示'}
        onCancel={() => {
          setTipModalShow(false);
          onClose?.();
        }}
      >
        <View className={'pay-tips pt-16 w-full'}>
          {tabList?.length > 0 ? (
            <>
              {/*<Cell.Group>*/}
              {/*  {tabList.map((item, index) => {*/}
              {/*    return (*/}
              {/*      <Cell*/}
              {/*        className='nutui-cell-clickable'*/}
              {/*        title={item.tipTitle}*/}
              {/*        key={index}*/}
              {/*        extra={<ArrowDown />}*/}
              {/*        onClick={() => {*/}
              {/*          getTabContent(item.id);*/}
              {/*        }}*/}
              {/*      >*/}
              {/*        <View className={'tips-content'}>*/}
              {/*          <RichText nodes={tabContent} />*/}
              {/*        </View>*/}
              {/*      </Cell>*/}
              {/*    );*/}
              {/*  })}*/}
              {/*</Cell.Group>*/}

              <Collapse accordion expandIcon={<ArrowDown />}>
                {tabList.map((item, index) => {
                  return (
                    <Collapse.Item
                      title={item.tipTitle}
                      name={item.tipTitle}
                      key={index}
                      onChange={() => {
                        getTabContent(item.id);
                      }}
                    >
                      <View className={'tips-content'}>
                        <RichText nodes={tabContent} />
                      </View>
                    </Collapse.Item>
                  );
                })}
              </Collapse>
            </>
          ) : (
            <View className={'tips-content'}>
              <RichText nodes={tabContent} />
            </View>
          )}
          {show && (
            <BottomCtr>
              <Button
                type={'primary'}
                className={'w-full mt-16 submit-btn'}
                onClick={() => {
                  getData?.(true);
                }}
                disabled={isDisabled}
              >
                我已阅读并同意{countNum > 0 && <Text className='timer-text'>({countNum}s)</Text>}
              </Button>
            </BottomCtr>
          )}
        </View>
      </CustomActionSheet>
    </>
  );
};
export default memo(ReadTips);
