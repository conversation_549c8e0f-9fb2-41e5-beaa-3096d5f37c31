import { useState } from 'react';
import cx from 'classnames';
import Taro, { useLoad, useRouter } from '@tarojs/taro';
import api from '@/api';
import { TravelProductListVO, TravelSortieListVO } from '@/api/servies/dkx';
import { SUCCESS_CODE } from '@/utils/constant';
import { toUrl } from '@/utils';
import PsgCard, { PassengerInfo } from '@/components/PsgCard';
import BottomCtr from '@/components/BottomCtr';
import CustomHeader from '@/components/CustomHeader';
import { Button, View, Text } from '@tarojs/components';
import PurchaseNotice from '../airTourDetail/PurchaseNotice';
import ContactInfoModal from '@/pages/jauntAirLine/components/ContactInfoModal';
import { ArrowRight } from '@nutui/icons-react-taro';
import '@/pages/jauntAirLine/passengerInfo/index.less';
import useDebounce from '@/hooks/useDebounce';
import ReadTips from '@/pages/orders/components/ReadTips';

const AirTourOrderDetail = () => {
  const router = useRouter();
  const { productNo, packageNo, productDate, packageData } = router.params;
  const _packageData = packageData ? JSON.parse(packageData) : {};

  const [sortieList, setSortieList] = useState<TravelSortieListVO[]>([]);
  const [selectedSortieNo, setSelectedSortieNo] = useState(''); // 选择的飞行架次
  const [productDetail, setProductDetail] = useState<TravelProductListVO>({});
  const [passengers, setPassengers] = useState<PassengerInfo[]>([]); // 乘机人列表
  // const [concatPassengers, setConcatPassengers] = useState<PassengerInfo[]>([]); // 联系人列表
  const [contactInfo, setContactInfo] = useState<any>({}); // 联系人信息 // 联系人列表
  const [contactInfoModalVisible, setContactInfoModalVisible] = useState(false); // 联系人信息弹窗
  const [submitLoading, setSubmitLoading] = useState(false);
  const [agreementChecked, setAgreementChecked] = useState(false); // 协议勾选
  const [tipModalShow, setTipModalShow] = useState(false); //须知弹窗

  // 架次列表
  const fetchSortieList = async params => {
    const { data, code } = await api.v10SortieListCreate({ ...params });

    if (code === SUCCESS_CODE) {
      setSortieList(data || []);
    }
  };

  // 产品详情
  const fetchProductDetail = async () => {
    if (productNo) {
      Taro.showLoading({
        title: '加载中',
      });
      const { data, code } = await api.v10ProductDetailCreate({ productNo }).finally(() => {
        Taro.hideLoading();
      });

      if (code === SUCCESS_CODE && data) {
        setProductDetail(data);
      }
    }
  };
  const isFormValid = () => {
    if (!selectedSortieNo) {
      Taro.showToast({
        title: '请选择飞行架次',
        icon: 'none',
      });
      return;
    }

    if (passengers.length === 0) {
      Taro.showToast({
        title: '请选择乘客',
        icon: 'none',
      });
      return;
    }

    if (!contactInfo.name || !contactInfo.mobile) {
      Taro.showToast({
        title: '请填写完整的联系人信息',
        icon: 'none',
      });
      return;
    }
    if (!agreementChecked) {
      Taro.showToast({
        title: '请阅读并同意协议！',
        icon: 'none',
      });
      return;
    }
    return true;
  };
  //提交订单
  const handleSubmitOrder = useDebounce(async () => {
    // Taro.showToast({
    //   title: '提交中',
    //   icon: 'loading',
    // });
    setSubmitLoading(true);
    const params: any = {
      price: Number(_packageData.totalPaymentPrice) * passengers.length,
      contactName: contactInfo?.name,
      contactPhone: contactInfo?.mobile,
      packageNo,
      productNo: productNo || '',
      productDate,
      orderType: productDetail.productType as number,
      salePassengersParam: passengers.map(item => ({
        ...item,
        price: Number(_packageData.totalPaymentPrice),
        packageNo,
        sortiesNo: selectedSortieNo,
      })),
      bookDate: productDate || '',
    };
    const { data, code } = await api.v10BookingCreate(params).finally(() => {
      // Taro.hideToast();
      setSubmitLoading(false);
    });

    if (code === SUCCESS_CODE && data) {
      toUrl(`/pages/jauntAirLine/payment/index?data=${JSON.stringify(data)}&pageType=travel`);
    }
  });

  useLoad(() => {
    fetchProductDetail();
    fetchSortieList({ productDate, productNo });
  });

  return (
    <View className='min-h-screen  bg-primary-linear passenger-info-page'>
      <CustomHeader showBackButton title='订单详情' bgColor='transparent' />

      <View className='px-16 py-12'>
        {/* 订单详情卡片 */}
        <View className='bg-white rounded-12 px-16 py-8 mb-12 shadow-sm'>
          <Text className='text-20 font-semibold text-1D1F20 mb-12 block'>
            {productDetail.productName}
          </Text>

          <View className='mt-12'>
            {[
              { label: '套餐类型', value: _packageData.packageName },
              { label: '套餐详情', value: _packageData.description },
              { label: '出行日期', value: productDate },
            ].map((item, index) => {
              return (
                <View
                  className={cx('flex', {
                    'mt-12': index !== 0,
                  })}
                  key={index}
                >
                  <View className='text-12 w-54'>{item.label}</View>
                  <Text className='text-12 font-medium'>{item.value}</Text>
                </View>
              );
            })}
          </View>
        </View>

        {/* 使用有效期卡片 */}
        <View className='bg-white rounded-12 px-16 py-8 mb-16 shadow-sm'>
          <View className='text-14 font-semibold text-1D1F20 mb-4'>使用有效期</View>

          {/* <View className='text-12 text-737578 mb-12'>2023.12.27 - 2024.06.27有效</View> */}
          <View className='mt-8 text-12 text-737578 mb-12'>
            产品有效期以产品日期为准，如需更改时间请提前联系客服。
          </View>

          {/* 六 购买须知 */}
          <PurchaseNotice
            className='mt-8'
            showNotice={false}
            payMustRead={productDetail.payMustRead}
          />
        </View>

        {/* 飞行架次 */}
        <View className='bg-white rounded-12 px-16 py-8 mb-16 shadow-sm'>
          <View className='text-14 font-semibold text-1D1F20 mb-12'>飞行架次</View>

          {/* 飞行架次列表 */}
          <View>
            {sortieList?.map((item, index) => (
              <View
                key={item.sortieNo}
                className={cx('rounded-8 px-8 py-6 flex flex-col', {
                  'bg-F2F6FC': selectedSortieNo !== item.sortieNo,
                  'bg-primary': selectedSortieNo === item.sortieNo,
                  'mt-8': index !== 0,
                })}
                onClick={() => {
                  setSelectedSortieNo(item.sortieNo || '');
                }}
              >
                <View className='flex justify-between items-center mb-4'>
                  <Text
                    className={cx('text-12 font-medium', {
                      'text-1D1F20': selectedSortieNo !== item.sortieNo,
                      'text-white': selectedSortieNo === item.sortieNo,
                    })}
                  >
                    {item.name}
                  </Text>
                  <Text
                    className={cx('text-10', {
                      'text-FF7346': selectedSortieNo !== item.sortieNo,
                      'text-white opacity-75': selectedSortieNo === item.sortieNo,
                    })}
                  >
                    剩余 {item.leftInventory}
                  </Text>
                </View>
                <View className='flex'>
                  <Text
                    className={cx('text-12 mr-12', {
                      'text-737578': selectedSortieNo !== item.sortieNo,
                      'text-white opacity-75': selectedSortieNo === item.sortieNo,
                    })}
                  >
                    飞行时间:{item.depTime}-{item.arrTime}
                  </Text>
                  <Text
                    className={cx('text-12', {
                      'text-737578': selectedSortieNo !== item.sortieNo,
                      'text-white opacity-75': selectedSortieNo === item.sortieNo,
                    })}
                  >
                    起降时间: 待定
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* 选择乘客 */}
        <View className='bg-white rounded-12 px-16 py-12 mb-16 shadow-sm'>
          <View className='flex items-center mb-12'>
            <Text className='text-FF7346 mr-2'>*</Text>
            <Text className='text-14 font-semibold text-1D1F20'>选择乘客</Text>
          </View>

          {/* 乘机人选择 */}
          <PsgCard
            title={false}
            passengers={passengers}
            onSelect={(data: PassengerInfo[]) => {
              setPassengers(data);
              if (data.length > 0) {
                setContactInfo({
                  name: data[0]?.name || `${data[0]?.firstName || ''} ${data[0]?.nextName || ''}`,
                  mobile: data[0]?.mobile,
                });
              }
            }}
          />
        </View>

        {/* 选择联系人 */}
        {/*   <View className='bg-white rounded-12 px-16 py-12 mb-16 shadow-sm'>
          <View className='flex items-center mb-12'>
            <Text className='text-FF7346 mr-2'>*</Text>
            <Text className='text-14 font-semibold text-1D1F20'>选择联系人</Text>
          </View>

           联系人选择
          <PsgCard
            title={false}
            passengers={concatPassengers}
            onSelect={(data: PassengerInfo[]) => {
              setConcatPassengers(data);
            }}
          />
        </View>*/}
        <View className='card-box contact-info-section'>
          <View className='section-title'>
            联系人信息 <Text className='section-subtitle'>用于接收航班信息</Text>
          </View>

          <View className='contact-info-item' onClick={() => setContactInfoModalVisible(true)}>
            <View className='contact-info-left'>
              <Text className='contact-label'>姓名</Text>
            </View>
            <View className='edit-icon'>
              <Text className='contact-value'>{contactInfo?.name}</Text>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>

          <View className='contact-info-item' onClick={() => setContactInfoModalVisible(true)}>
            <View className='contact-info-left'>
              <Text className='contact-label'>联系电话</Text>
            </View>
            <View className='edit-icon'>
              <Text className='contact-value'>+86 {contactInfo?.mobile}</Text>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>
        </View>

        {/* 保险 */}
        {/* <View className='bg-white rounded-12 px-16 py-12 mb-16 shadow-sm'>
          <View className='flex items-center mb-12'>
            <Text className='text-FF7346 mr-2'>*</Text>
            <Text className='text-14 font-semibold text-1D1F20'>保险</Text>
          </View>

          <View className='bg-F2F6FC rounded-8 px-8 py-6 flex items-center justify-between'>
            <View>
              <View className='flex items-center'>
                <Text className='text-12 text-1D1F20'>门票意外险</Text>
                <Text className='text-10 text-737578 ml-4'>3元/份起步</Text>
              </View>
              <Text className='text-10 text-737578 mt-4'>(意外事故10万,社保内医疗1万)</Text>
            </View>

            <View className='w-14 h-14 rounded-full border-1 border-ACADFF flex items-center justify-center'></View>
          </View>
        </View> */}

        {/*/!* 协议勾选 *!/*/}
        <ReadTips
          show={tipModalShow}
          onClose={() => setTipModalShow(false)}
          defaultData={{ companyCode: productDetail?.companyCode }}
          pageType={2}
          getData={isMustRead => {
            if (isMustRead) {
              setTipModalShow(false);
              handleSubmitOrder();
            }
          }}
          onChecked={checked => setAgreementChecked(checked)}
        />
      </View>

      <BottomCtr className='flex justify-between items-center'>
        <View className='flex'>
          <Text>实付</Text>
          <Text>¥ {Number(_packageData.totalPaymentPrice) * passengers.length}</Text>
        </View>
        <View>
          <Button
            className='h-48 rounded-full bg-primary px-32 text-white flex items-center text-16 font-semibold'
            loading={submitLoading}
            onClick={() => {
              if (!isFormValid()) return;
              setTipModalShow(true);
            }}
          >
            提交订单
          </Button>
        </View>
      </BottomCtr>
      {/*联系人信息弹窗*/}
      <ContactInfoModal
        show={contactInfoModalVisible}
        onClose={() => setContactInfoModalVisible(false)}
        defaultValues={contactInfo}
        getData={val => {
          setContactInfo(val);
        }}
      />
    </View>
  );
};

export default AirTourOrderDetail;
